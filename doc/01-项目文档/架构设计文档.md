# A股智能选股系统 - 架构设计文档

## 1. 架构概述

### 1.1 设计原则
- **接口抽象原则**：通过抽象接口隔离具体实现，提高系统可扩展性
- **依赖隔离原则**：模块间通过接口通信，降低耦合度
- **单一职责原则**：每个模块职责明确，便于维护和测试
- **开闭原则**：对扩展开放，对修改封闭

### 1.2 整体架构
采用三大核心功能模块架构：
- **数据更新模块**：历史数据更新和增量数据更新，批量处理，智能缓存
- **策略选股模块**：多策略支持，智能评分，候选池控制
- **可视化验证模块**：K线图表，策略可视化展示

### 1.3 技术架构
采用分层架构模式，从上到下分为：
- **应用层**：主程序入口、策略选股、可视化展示
- **服务层**：优化数据服务、策略管理、可视化管理
- **数据层**：数据获取、数据存储、数据访问
- **基础设施层**：具体实现（Tushare、MySQL、SQLite）

## 2. 三大核心功能模块

### 2.1 数据更新模块 (Data Update Module)
```
┌─────────────────────────────────────────────────────────────┐
│                    数据更新模块                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   main.py       │    │OptimizedDataSvc │                │
│  │   主程序入口     │    │ 优化数据服务     │                │
│  └─────────────────┘    └─────────────────┘                │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ TushareSource   │    │  DataAccess     │                │
│  │ Tushare数据源   │    │  数据访问层     │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

**核心功能**：
- 增量更新T-1交易日数据
- 历史数据批量补充
- 智能缓存和批量处理
- 错误隔离和进度监控

### 2.2 策略选股模块 (Strategy Selection Module)
```
┌─────────────────────────────────────────────────────────────┐
│                    策略选股模块                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ run_selection.py│    │ StrategyManager │                │
│  │ 选股主程序       │    │ 策略管理器       │                │
│  └─────────────────┘    └─────────────────┘                │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │TechnicalReversal│    │ VolumeAnomaly   │                │
│  │ 技术反转策略     │    │ 成交量异常策略   │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

**核心功能**：
- 多策略支持和管理
- 智能评分和排序
- 候选股票池控制
- ST股票自动过滤

### 2.3 可视化验证模块 (Visual Validation Module)
```
┌─────────────────────────────────────────────────────────────┐
│                  可视化验证模块                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │InteractiveVisual│    │ VisualValidator │                │
│  │ 交互式验证工具   │    │ 可视化验证器     │                │
│  └─────────────────┘    └─────────────────┘                │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ ChartGenerator  │    │ VisualManager   │                │
│  │ 图表生成器       │    │ 可视化管理器     │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

**核心功能**：
- K线图表专业可视化
- 策略信号标注和分析
- 批量验证和比较
- 交互式操作界面

## 3. 分层架构设计

### 3.1 应用层 (Application Layer)
```
┌─────────────────────────────────────────────────────────────┐
│                        应用层                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   main.py       │    │run_selection.py │                │
│  │   数据更新       │    │   策略选股       │                │
│  └─────────────────┘    └─────────────────┘                │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │interactive_*.py │    │ system_check.py │                │
│  │ 可视化验证工具   │    │ 系统检查工具     │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 服务层 (Service Layer)
```
┌─────────────────────────────────────────────────────────────┐
│                        服务层                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │OptimizedDataSvc │    │ StrategyManager │                │
│  │ 优化数据服务     │    │ 策略管理器       │                │
│  └─────────────────┘    └─────────────────┘                │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ValidationManager│    │ TushareConfig   │                │
│  │ 验证管理器       │    │ 配置管理器       │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

## 4. 核心接口设计

### 4.1 数据源接口 (IDataSource)
```python
from abc import ABC, abstractmethod
from typing import List, Dict, Optional
from datetime import datetime

class IDataSource(ABC):
    """数据源抽象接口"""

    @abstractmethod
    def get_stock_list(self) -> List[Dict]:
        """获取股票列表"""
        pass

    @abstractmethod
    def get_daily_data(self, stock_code: str,
                      start_date: datetime,
                      end_date: datetime) -> List[Dict]:
        """获取日K线数据"""
        pass

    @abstractmethod
    def batch_get_daily_data(self, requests: List[tuple]) -> Dict[str, List[Dict]]:
        """批量获取股票日K线数据"""
        pass

    @abstractmethod
    def get_trading_calendar(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """获取交易日历"""
        pass
```

### 4.2 数据访问接口 (IDataAccess)
```python
class IDataAccess(ABC):
    """数据访问抽象接口"""

    @abstractmethod
    def save_stock_info(self, stock_info: Dict) -> bool:
        """保存股票基本信息"""
        pass

    @abstractmethod
    def save_daily_data(self, daily_data: List[Dict]) -> bool:
        """保存日交易数据"""
        pass

    @abstractmethod
    def get_stock_data(self, stock_code: str,
                      start_date: datetime,
                      end_date: datetime) -> List[Dict]:
        """查询股票数据"""
        pass

    @abstractmethod
    def batch_get_latest_trade_dates(self, stock_codes: List[str]) -> Dict[str, Optional[datetime]]:
        """批量获取股票最新交易日期"""
        pass

    @abstractmethod
    def batch_get_missing_dates(self, stock_codes: List[str],
                               start_date: datetime, end_date: datetime) -> Dict[str, List[datetime]]:
        """批量获取缺失日期"""
        pass

    @abstractmethod
    def get_stocks_need_update(self) -> List[str]:
        """获取需要更新数据的股票列表"""
        pass
```

### 4.3 选股策略接口 (ISelectionStrategy)
```python
class ISelectionStrategy(ABC):
    """选股策略抽象接口"""

    @abstractmethod
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        pass

    @abstractmethod
    def get_strategy_description(self) -> str:
        """获取策略描述"""
        pass

    @abstractmethod
    def select_stocks(self, **kwargs) -> List[Dict]:
        """执行选股逻辑"""
        pass

    @abstractmethod
    def get_config(self) -> Dict:
        """获取策略配置"""
        pass
```

### 4.4 可视化接口 (IVisualizer)
```python
class IVisualizer(ABC):
    """可视化抽象接口"""

    @abstractmethod
    def generate_chart(self, stock_code: str, start_date: datetime,
                      end_date: datetime, strategy_signals: List[Dict] = None) -> str:
        """生成K线图表"""
        pass

    @abstractmethod
    def save_chart(self, chart_path: str, stock_code: str,
                  start_date: datetime, end_date: datetime) -> bool:
        """保存图表到文件"""
        pass
```

## 4. 模块详细设计

### 4.1 数据获取模块
```
DataSourceManager
├── IDataSource (接口)
├── AkshareDataSource (实现)
├── DataValidator (数据验证)
└── DataConverter (数据转换)
```

**核心类设计**：
- `DataSourceManager`: 数据源管理器，负责数据源的注册和调用
- `AkshareDataSource`: akshare数据源具体实现
- `DataValidator`: 数据质量验证
- `DataConverter`: 数据格式转换和标准化

### 4.2 数据存储模块
```
DataAccessManager
├── IDataAccess (接口)
├── SQLiteDataAccess (实现)
├── ConnectionPool (连接池)
└── DatabaseMigration (数据库迁移)
```

**核心类设计**：
- `DataAccessManager`: 数据访问管理器
- `SQLiteDataAccess`: SQLite数据访问具体实现
- `ConnectionPool`: 数据库连接池管理
- `DatabaseMigration`: 数据库版本管理和迁移

### 4.3 选股策略模块
```
StrategyManager
├── ISelectionStrategy (接口)
├── VolumeAnomalyStrategy (交易量异动策略)
├── StrategyFactory (策略工厂)
└── StrategyConfig (策略配置)
```

**核心类设计**：
- `StrategyManager`: 策略管理器，负责策略的加载和执行
- `VolumeAnomalyStrategy`: 交易量异动选股策略实现
- `StrategyFactory`: 策略工厂，动态创建策略实例
- `StrategyConfig`: 策略配置管理

### 4.4 定时任务模块
```
SchedulerManager
├── TaskScheduler (任务调度器)
├── TaskExecutor (任务执行器)
├── TaskMonitor (任务监控)
└── TaskConfig (任务配置)
```

**核心类设计**：
- `TaskScheduler`: 基于APScheduler的任务调度器
- `TaskExecutor`: 任务执行器，负责具体任务的执行
- `TaskMonitor`: 任务监控，记录执行状态和性能指标
- `TaskConfig`: 任务配置管理

### 4.5 通知模块
```
NotificationManager
├── INotifier (接口)
├── ConsoleNotifier (控制台通知)
├── EmailNotifier (邮件通知)
└── NotificationTemplate (通知模板)
```

**核心类设计**：
- `NotificationManager`: 通知管理器
- `ConsoleNotifier`: 控制台输出实现
- `EmailNotifier`: 邮件通知实现
- `NotificationTemplate`: 通知内容模板管理

## 5. 数据模型设计

### 5.1 数据库表结构

#### 股票基本信息表 (stock_info)
```sql
CREATE TABLE stock_info (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    stock_code VARCHAR(10) NOT NULL UNIQUE,
    stock_name VARCHAR(50) NOT NULL,
    industry VARCHAR(50),
    market VARCHAR(10),
    list_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 日交易数据表 (daily_trading)
```sql
CREATE TABLE daily_trading (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    stock_code VARCHAR(10) NOT NULL,
    trade_date DATE NOT NULL,
    open_price DECIMAL(10,2),
    close_price DECIMAL(10,2),
    high_price DECIMAL(10,2),
    low_price DECIMAL(10,2),
    volume BIGINT,
    amount DECIMAL(15,2),
    turnover_rate DECIMAL(8,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(stock_code, trade_date)
);
```

#### 选股结果表 (selection_results)
```sql
CREATE TABLE selection_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    strategy_name VARCHAR(50) NOT NULL,
    stock_code VARCHAR(10) NOT NULL,
    selection_date DATE NOT NULL,
    score DECIMAL(8,4),
    reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 系统配置表 (system_config)
```sql
CREATE TABLE system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 5.2 数据传输对象 (DTO)

#### StockInfo DTO
```python
@dataclass
class StockInfo:
    stock_code: str
    stock_name: str
    industry: str
    market: str
    list_date: datetime
```

#### DailyTrading DTO
```python
@dataclass
class DailyTrading:
    stock_code: str
    trade_date: datetime
    open_price: float
    close_price: float
    high_price: float
    low_price: float
    volume: int
    amount: float
    turnover_rate: float
```

#### SelectionResult DTO
```python
@dataclass
class SelectionResult:
    strategy_name: str
    stock_code: str
    stock_name: str
    selection_date: datetime
    score: float
    reason: str
```

## 6. 配置管理设计

### 6.1 配置文件结构
```yaml
# config/app_config.yaml
app:
  name: "A股智能选股系统"
  version: "1.0.0"
  debug: false

database:
  type: "sqlite"
  path: "data/stock_selection.db"
  pool_size: 10

data_source:
  type: "akshare"
  retry_times: 3
  timeout: 30

scheduler:
  timezone: "Asia/Shanghai"
  jobs:
    - name: "daily_data_fetch"
      trigger: "cron"
      hour: 16
      minute: 0
    - name: "stock_selection"
      trigger: "cron"
      hour: 16
      minute: 30

notification:
  console:
    enabled: true
    level: "INFO"
  email:
    enabled: true
    smtp_server: "smtp.qq.com"
    smtp_port: 587
    username: ""
    password: ""
    recipients: []

strategies:
  volume_anomaly:
    enabled: true
    params:
      base_period_days: 14
      volume_threshold: 0.7
    multiplier: 3.0
    price_range:
      min: 5.0
      max: 100.0
```

### 6.2 配置管理类设计
```python
class ConfigManager:
    """配置管理器"""

    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = self._load_config()

    def get_database_config(self) -> Dict:
        """获取数据库配置"""
        return self.config.get('database', {})

    def get_strategy_config(self, strategy_name: str) -> Dict:
        """获取策略配置"""
        return self.config.get('strategies', {}).get(strategy_name, {})

    def get_notification_config(self) -> Dict:
        """获取通知配置"""
        return self.config.get('notification', {})
```

## 7. 项目目录结构

```
select-in-ai/
├── README.md
├── requirements.txt
├── setup.py
├── config/
│   ├── app_config.yaml
│   └── logging_config.yaml
├── data/
│   └── stock_selection.db
├── doc/
│   ├── PRD_A股选股系统.md
│   ├── 架构设计文档.md
│   └── API文档.md
├── logs/
│   ├── app.log
│   └── error.log
├── src/
│   ├── __init__.py
│   ├── main.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── interfaces/
│   │   │   ├── __init__.py
│   │   │   ├── data_source.py
│   │   │   ├── data_access.py
│   │   │   ├── strategy.py
│   │   │   └── notifier.py
│   │   ├── models/
│   │   │   ├── __init__.py
│   │   │   ├── stock_info.py
│   │   │   ├── daily_trading.py
│   │   │   └── selection_result.py
│   │   └── exceptions/
│   │       ├── __init__.py
│   │       └── custom_exceptions.py
│   ├── data/
│   │   ├── __init__.py
│   │   ├── sources/
│   │   │   ├── __init__.py
│   │   │   ├── akshare_source.py
│   │   │   └── data_source_manager.py
│   │   └── access/
│   │       ├── __init__.py
│   │       ├── sqlite_access.py
│   │       └── data_access_manager.py
│   ├── strategies/
│   │   ├── __init__.py
│   │   ├── volume_anomaly_strategy.py
│   │   ├── strategy_factory.py
│   │   └── strategy_manager.py
│   ├── scheduler/
│   │   ├── __init__.py
│   │   ├── task_scheduler.py
│   │   ├── task_executor.py
│   │   └── task_monitor.py
│   ├── notification/
│   │   ├── __init__.py
│   │   ├── console_notifier.py
│   │   ├── email_notifier.py
│   │   └── notification_manager.py
│   ├── config/
│   │   ├── __init__.py
│   │   └── config_manager.py
│   └── utils/
│       ├── __init__.py
│       ├── logger.py
│       ├── date_utils.py
│       └── validators.py
├── tests/
│   ├── __init__.py
│   ├── unit/
│   │   ├── test_data_sources.py
│   │   ├── test_strategies.py
│   │   └── test_data_access.py
│   ├── integration/
│   │   ├── test_end_to_end.py
│   │   └── test_scheduler.py
│   └── fixtures/
│       └── sample_data.py
└── scripts/
    ├── init_database.py
    ├── data_migration.py
    └── run_tests.py
```

## 8. 部署架构

### 8.1 单机部署架构
```
┌─────────────────────────────────────────────────────────────┐
│                    服务器环境                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  Python应用     │    │  SQLite数据库   │                │
│  │  (选股系统)     │────│  (本地文件)     │                │
│  └─────────────────┘    └─────────────────┘                │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  定时任务       │    │  日志文件       │                │
│  │  (APScheduler)  │    │  (本地存储)     │                │
│  └─────────────────┘    └─────────────────┘                │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  邮件服务       │    │  配置文件       │                │
│  │  (SMTP)         │    │  (YAML)         │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

### 8.2 容器化部署架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Docker容器                               │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  应用容器       │    │  数据卷         │                │
│  │  (Python App)   │────│  (SQLite + Logs)│                │
│  └─────────────────┘    └─────────────────┘                │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  配置卷         │    │  网络           │                │
│  │  (Config Files) │    │  (Host Network) │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

## 9. 性能和扩展性设计

### 9.1 性能优化策略
- **数据库优化**：
  - 为常用查询字段建立索引
  - 使用连接池管理数据库连接
  - 批量插入数据以提高写入性能

- **并发处理**：
  - 使用线程池并发获取股票数据
  - 异步处理非关键任务
  - 合理控制并发数量避免API限制

- **缓存策略**：
  - 缓存股票基本信息
  - 缓存计算结果避免重复计算
  - 使用内存缓存提高查询速度

### 9.2 扩展性设计
- **水平扩展**：
  - 支持多策略并行运行
  - 支持分布式任务调度
  - 支持数据分片存储

- **垂直扩展**：
  - 插件化架构支持新增功能模块
  - 接口抽象支持新增数据源和存储
  - 配置化管理支持灵活调整

## 10. 安全性设计

### 10.1 数据安全
- **敏感信息加密**：邮件密码等敏感配置加密存储
- **数据备份**：定期备份数据库文件
- **访问控制**：限制数据库文件访问权限

### 10.2 系统安全
- **异常处理**：完善的异常捕获和处理机制
- **日志审计**：记录关键操作和异常信息
- **资源限制**：控制内存和CPU使用量

## 11. 监控和运维

### 11.1 监控指标
- **系统指标**：CPU、内存、磁盘使用率
- **业务指标**：数据获取成功率、选股命中数量
- **性能指标**：任务执行时间、数据库查询性能

### 11.2 日志管理
- **分级日志**：DEBUG、INFO、WARNING、ERROR
- **日志轮转**：按大小和时间自动轮转日志文件
- **结构化日志**：使用JSON格式便于分析

### 11.3 告警机制
- **任务失败告警**：定时任务执行失败时发送邮件
- **数据异常告警**：数据获取异常时及时通知
- **系统异常告警**：系统资源不足时预警

---

**文档版本**：v1.0
**创建日期**：2024年12月
**最后更新**：架构设计完成，准备开始详细设计和编码
