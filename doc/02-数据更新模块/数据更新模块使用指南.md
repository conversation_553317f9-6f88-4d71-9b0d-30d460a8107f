# 数据更新模块使用指南

## 🎯 模块概述

数据更新模块是A股智能选股系统的核心基础模块，负责从外部数据源获取股票交易数据并存储到本地数据库。该模块专注于T-1交易日数据获取，支持增量更新和历史数据补充两种模式。

本模块使用Tushare作为主要数据源，提供稳定可靠的A股数据服务。

## 🚀 快速开始

### 前置条件

#### 1. 环境准备
```bash
# 激活虚拟环境
source venv/bin/activate

# 确认数据库连接正常
python scripts/test_database.py
```

#### 2. Tushare配置
在使用数据更新功能前，需要先配置Tushare Token：

```bash
# 使用配置脚本（推荐）
python scripts/setup_tushare_token.py

# 或快速设置
python scripts/setup_tushare_token.py your_token_here

# 验证配置
python scripts/test_tushare.py
```

### 基本使用
```bash
# 更新股票列表
python src/main.py --update-stocks

# 增量更新T-1交易日数据
python src/main.py --incremental

# 补充历史数据（默认最近90天）
python src/main.py --historical

# 查看数据统计
python src/main.py --stats
```

## 📋 功能详解

### 1. 增量更新功能

**适用场景**: 每日定时任务，获取最新交易日数据

**核心特性**:
- 智能检测需要更新的股票
- 只获取T-1交易日数据
- 批量处理，每批100只股票
- 错误隔离，单只股票失败不影响整批

**使用方法**:
```bash
# 基本增量更新
python src/main.py --incremental

# 增量更新并显示统计信息
python src/main.py --incremental --stats
```

**工作流程**:
1. 获取最新交易日期
2. 批量检查所有股票的最新数据日期
3. 识别需要更新的股票
4. 分批处理，批量获取数据
5. 保存到数据库

### 2. 历史数据补充功能

**适用场景**: 系统初始化、数据修复、特定时间段数据补充

**核心特性**:
- 支持自定义时间范围
- 支持指定特定股票
- 智能缺失检测
- 批量处理优化

**使用方法**:
```bash
# 补充所有股票最近90天数据（默认）
python src/main.py --historical

# 补充指定时间范围的历史数据
python src/main.py --historical --start-date 2024-01-01 --end-date 2024-03-31

# 补充指定股票的历史数据
python src/main.py --historical --stocks 000001,000002,600000 --start-date 2024-01-01

# 补充指定股票指定时间范围的历史数据
python src/main.py --historical --stocks 000001,000002 --start-date 2024-01-01 --end-date 2024-03-31
```

**参数说明**:
- `--start-date`: 开始日期，格式YYYY-MM-DD，默认为90天前
- `--end-date`: 结束日期，格式YYYY-MM-DD，默认为当前日期
- `--stocks`: 股票代码列表，用逗号分隔，默认为所有股票

### 3. 股票列表更新功能

**适用场景**: 系统初始化、定期更新股票基本信息

**使用方法**:
```bash
# 更新股票列表
python src/main.py --update-stocks

# 更新股票列表并显示统计
python src/main.py --update-stocks --stats
```

### 4. 数据统计功能

**适用场景**: 数据质量检查、系统状态监控

**使用方法**:
```bash
# 显示数据统计信息
python src/main.py --stats
```

**统计内容**:
- 股票总数
- 数据日期范围
- 各股票数据完整性
- 最新更新时间

## 🔧 配置管理

### Tushare数据源配置

#### 获取Tushare Token

1. 访问 [Tushare官网](https://tushare.pro/)
2. 注册账号并登录
3. 在个人中心获取API Token
4. 记录下您的Token（格式类似：`xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`）

#### Token配置方式

**方法一：使用配置脚本（推荐）**

```bash
# 交互式配置
python scripts/setup_tushare_token.py

# 快速配置
python scripts/setup_tushare_token.py your_token_here
```

配置脚本功能特性：
- 🔧 交互式菜单操作
- 🔍 Token格式验证
- 🌐 连接测试
- 💾 自动保存到配置文件
- 🗑️ 配置管理（查看、删除）

**方法二：手动配置文件**

```bash
# 1. 复制模板
cp config/tushare_token.json.example config/tushare_token.json

# 2. 编辑配置文件
{
  "token": "your_real_token_here"
}
```

**方法三：环境变量（备用）**

```bash
export TUSHARE_TOKEN="your_token_here"
```

#### 配置文件结构

- **配置文件位置**: `config/tushare_token.json`
- **模板文件**: `config/tushare_token.json.example`
- **配置优先级**: 配置文件 > 环境变量

#### 配置验证

```bash
# 基本功能测试
python scripts/test_tushare_basic.py

# 完整功能测试（需要有效Token）
python scripts/test_tushare_source.py
```

### 数据库配置
配置文件位置: `config/database_config.json`

```json
{
    "mysql": {
        "host": "localhost",
        "port": 3306,
        "user": "agent",
        "password": "123456",
        "database": "trade-ai"
    }
}
```

### 数据源特性

| 特性 | Akshare | Tushare |
|------|---------|---------|
| 数据稳定性 | 中等 | 高 |
| API限制 | 较少 | 有限制但合理 |
| 数据质量 | 良好 | 优秀 |
| 更新频率 | 实时 | 实时 |
| 历史数据 | 丰富 | 非常丰富 |
| 技术指标 | 基础 | 丰富 |

## 📊 性能优化

### 批量处理
- 每批处理100只股票
- 批次间1-2秒延时
- 随机延时0.3-0.8秒

### 缓存机制
- 股票列表缓存
- 交易日历缓存
- 智能缓存清理

### 请求频率控制
- 每分钟最多500次请求
- 自动重试机制
- 错误隔离处理

## 💡 使用建议

### 日常运维
1. **每日增量更新**: 建议在交易日结束后运行
2. **定期数据检查**: 每周运行一次历史数据补充
3. **监控日志**: 关注日志输出，及时发现异常

### 初始化部署
1. **股票列表**: 首先更新股票列表
2. **历史数据**: 根据需要获取历史数据（建议从最近3个月开始）
3. **数据验证**: 运行测试脚本验证数据完整性

### 故障恢复
1. **网络异常**: 重新运行相应的更新命令
2. **数据缺失**: 使用历史数据补充功能修复
3. **数据库异常**: 检查数据库连接和权限

## ❓ 常见问题

### Q1: Token配置问题

**问题**: `Tushare token未配置`
**解决**:
```bash
# 运行配置脚本
python scripts/setup_tushare_token.py
```

**问题**: `Token无效`
**解决**:
- 检查Token是否正确复制
- 确认Tushare账号状态正常
- 检查API调用次数是否超限

**问题**: 配置文件不存在
**解决**:
```bash
# 运行配置脚本创建
python scripts/setup_tushare_token.py
```

### Q2: 数据获取失败怎么办？
```bash
# 检查网络连接
ping tushare.pro

# 检查Token配置
python scripts/test_tushare.py

# 检查数据库连接
python scripts/test_database.py
```

### Q3: 网络相关问题

**问题**: `连接超时`
**解决**:
- 检查网络连接
- 增加API超时配置值
- 检查防火墙设置

**问题**: `请求频率过高`
**解决**:
- 增加请求延时
- 减少并发数
- 检查频率限制设置

### Q4: 数据不完整怎么办？
```bash
# 检查数据统计
python src/main.py --stats

# 补充缺失数据
python src/main.py --historical --start-date 2024-01-01 --end-date 2024-03-31
```

### Q5: 如何提高数据获取速度？
- 确保网络连接稳定
- 使用增量更新而非全量更新
- 避免在交易时间获取数据
- 合理设置批量处理参数

## 🔍 测试验证

### 功能测试
```bash
# 运行数据更新测试
python scripts/test_data_update.py

# 测试交易日历功能
python scripts/test_trading_calendar.py
```

### 数据验证
```bash
# 验证特定股票数据
python src/main.py --historical --stocks 000001 --start-date 2024-01-01 --end-date 2024-01-31

# 检查数据完整性
python scripts/check_data_integrity.py
```

## 🔐 安全性说明

### Token安全
- 配置文件已添加到`.gitignore`，不会被提交到版本控制
- 只有模板文件会被提交，不包含真实Token
- 配置脚本中Token显示为掩码格式
- 支持隐藏输入模式
- 自动验证Token格式

### 最佳实践
1. **定期检查Token有效性**
   ```bash
   python scripts/setup_tushare_token.py
   # 选择选项3测试连接
   ```

2. **备份配置**
   ```bash
   # 备份配置文件（注意安全）
   cp config/tushare_token.json config/tushare_token.json.backup
   ```

3. **团队协作**
   - 每个开发者使用自己的Token
   - 不要共享Token配置文件
   - 使用模板文件指导配置

## 📞 技术支持

如遇问题，请：
1. 查看日志文件 `logs/app.log`
2. 运行相关测试脚本
3. 检查配置文件设置
4. 确认网络和数据库连接
5. 参考Tushare官方文档

## 📋 注意事项

1. **API限制**: Tushare对免费用户有API调用次数限制，请合理使用
2. **数据延时**: 部分数据可能有轻微延时，这是正常现象
3. **Token安全**: 请妥善保管您的Token，不要在代码中硬编码
4. **备份数据**: 建议定期备份重要的历史数据

---

**推荐使用流程**:
1. **新系统**: 配置Token → 更新股票列表 → 历史数据补充 → 增量更新
2. **日常维护**: 增量更新 → 定期数据检查
3. **故障恢复**: 诊断问题 → 数据修复 → 验证完整性
