# 数据获取功能说明

## 概述

A股智能选股系统采用优化的数据获取架构，专注于T-1交易日数据获取，支持两种主要的数据获取模式：

1. **增量更新**：智能检测并更新T-1交易日数据，适用于每日定时任务
2. **历史数据补充**：批量获取指定时间范围的历史数据，适用于数据初始化和补充

## 核心特性

### 🚀 性能优化
- **批量处理**：每批处理100只股票，提高处理效率
- **智能缓存**：股票列表和交易日历缓存，减少重复请求
- **随机延时**：0.3-0.8秒随机延时，避免规律性请求
- **并发控制**：合理的并发线程数，平衡效率和稳定性

### 🛡️ 稳定性保障
- **错误隔离**：单只股票失败不影响整批处理
- **重试机制**：失败重试采用递增延时策略
- **API限制控制**：每分钟最多500次请求，避免触发限制

### 📊 专业化设计
- **专注T-1数据**：移除实时数据功能，专注历史交易数据
- **交易日历智能**：基于真实交易日历，自动排除节假日和周末
- **数据完整性**：智能检测缺失数据，确保数据完整性

## 功能模块

### 🔄 增量更新功能

#### 核心特性
- **智能检测**：自动识别需要更新数据的股票
- **T-1数据专注**：只获取最新交易日的数据
- **批量处理**：分批处理股票，提高效率
- **缓存优化**：利用缓存减少重复请求

#### 使用场景
- 每日定时任务运行
- 系统维护后的数据同步
- 确保数据的时效性

#### 命令示例
```bash
# 执行增量数据更新
python src/main.py --incremental
```

#### 工作流程
1. 获取最新交易日期
2. 批量检查所有股票的最新数据日期
3. 识别需要更新的股票
4. 分批处理，批量获取数据
5. 保存到数据库

### 📊 历史数据补充功能

#### 核心特性
- **时间范围指定**：支持自定义开始和结束日期
- **股票范围指定**：可指定特定股票或获取所有股票
- **智能缺失检测**：批量检测指定范围内的缺失数据
- **批量处理**：分批处理多只股票，每批100只
- **进度监控**：详细的处理进度和统计信息

#### 使用场景
- 系统初始化时获取历史数据
- 补充特定时间段的缺失数据
- 为新增股票获取历史数据
- 数据质量检查和修复

#### 命令示例
```bash
# 补充所有股票最近90天的历史数据（默认）
python src/main.py --historical

# 补充指定时间范围的历史数据
python src/main.py --historical --start-date 2024-01-01 --end-date 2024-03-31

# 补充指定股票的历史数据
python src/main.py --historical --stocks 000001,000002,600000 --start-date 2024-01-01

# 补充指定股票指定时间范围的历史数据
python src/main.py --historical --stocks 000001,000002 --start-date 2024-01-01 --end-date 2024-03-31
```

#### 工作流程
1. 解析时间范围和股票范围参数
2. 获取指定范围内的股票列表
3. 分批处理股票（每批100只）
4. 批量检测缺失日期
5. 批量获取数据并保存到数据库

#### 参数说明
- `--start-date`：开始日期，格式为 YYYY-MM-DD，默认为90天前
- `--end-date`：结束日期，格式为 YYYY-MM-DD，默认为当前日期
- `--stocks`：股票代码列表，用逗号分隔，默认为所有股票

## 技术实现

### 核心架构

#### OptimizedDataService
系统采用 `OptimizedDataService` 作为核心数据服务，提供：
- 批量数据处理
- 智能缓存管理
- 错误隔离机制
- 进度监控

#### 主要接口方法

**数据源接口 (IDataSource)**
```python
def get_trading_calendar(self, start_date: datetime, end_date: datetime) -> List[datetime]:
    """获取交易日历（排除节假日和周末）"""

def batch_get_daily_data(self, requests: List[tuple]) -> Dict[str, List[Dict]]:
    """批量获取股票日K线数据"""
```

**数据访问接口 (IDataAccess)**
```python
def batch_get_latest_trade_dates(self, stock_codes: List[str]) -> Dict[str, Optional[datetime]]:
    """批量获取股票最新交易日期"""

def batch_get_missing_dates(self, stock_codes: List[str], start_date: datetime, end_date: datetime) -> Dict[str, List[datetime]]:
    """批量获取缺失日期"""

def get_stocks_need_update(self) -> List[str]:
    """获取需要更新数据的股票列表"""
```

### 核心算法

#### 优化增量更新算法
1. 获取最新交易日期
2. 批量获取所有股票的最新数据日期
3. 分批处理需要更新的股票（每批100只）
4. 批量获取数据并保存

#### 优化历史数据补充算法
1. 分批处理股票列表（每批100只）
2. 批量检测缺失日期
3. 批量获取数据
4. 错误隔离处理

### 性能优化

#### 批量处理优化
- **分批大小**：每批100只股票，平衡效率和稳定性
- **并发控制**：最多3个并发线程
- **错误隔离**：单只股票失败不影响整批处理
- **进度监控**：详细的处理进度日志

#### 缓存优化
- **股票列表缓存**：避免重复获取股票列表
- **交易日历缓存**：按年缓存，减少重复请求
- **智能缓存清理**：提供手动清理接口

#### 请求频率控制
- **随机延时**：0.3-0.8秒随机间隔
- **批次间延时**：批次间1-2秒延时
- **API限制**：每分钟最多500次请求

## 使用建议

### 日常运维
1. **每日增量更新**：建议在交易日结束后运行增量更新
2. **定期数据检查**：定期运行历史数据补充检查数据完整性
3. **监控日志**：关注日志输出，及时发现和处理异常

### 初始化部署
1. **股票列表**：首先更新股票列表
2. **历史数据**：根据需要获取历史数据（建议从最近3个月开始）
3. **数据验证**：运行测试脚本验证数据完整性

### 故障恢复
1. **网络异常**：重新运行相应的更新命令
2. **数据缺失**：使用历史数据补充功能修复
3. **数据库异常**：检查数据库连接和权限

## 命令行参考

### 完整参数列表
```bash
python src/main.py [选项]

选项:
  --update-stocks     更新股票列表
  --incremental       增量更新T-1交易日数据
  --historical        历史数据补充
  --start-date DATE   开始日期（YYYY-MM-DD格式）
  --end-date DATE     结束日期（YYYY-MM-DD格式）
  --stocks CODES      股票代码列表（逗号分隔）
  --stats            显示统计信息
  --help             显示帮助信息
```

### 常用组合
```bash
# 完整的数据初始化
python src/main.py --update-stocks --historical --start-date 2024-01-01

# 日常维护
python src/main.py --incremental --stats

# 数据修复
python src/main.py --historical --stocks 000001,000002 --start-date 2024-01-01 --end-date 2024-03-31
```

## 测试验证

### 功能测试
运行测试脚本验证功能：
```bash
python scripts/test_data_update.py
```

测试内容包括：
- 交易日历功能测试
- 缺失日期检测测试
- 需要更新股票检测测试
- 增量更新逻辑测试

### 数据验证
```bash
# 查看系统统计信息
python src/main.py --stats

# 检查特定股票的数据完整性
python src/main.py --historical --stocks 000001 --start-date 2024-01-01 --end-date 2024-01-31
```

## 注意事项

1. **数据源**：系统使用Tushare作为主要数据源，需要配置Token
2. **API限制**：Tushare有请求频率限制，系统已内置频率控制
3. **网络稳定性**：确保网络连接稳定，避免数据获取中断
4. **数据库权限**：确保MySQL数据库用户有足够的读写权限
5. **磁盘空间**：历史数据较大，确保有足够的存储空间
6. **专注T-1数据**：系统专注历史交易数据，不提供实时数据

## 版本信息

- **功能版本**：v2.0
- **更新日期**：2024年12月
- **主要改进**：采用优化的批量处理架构，删除过时实现
