# A股智能选股系统 - 文档导航

## 📚 文档目录结构

本文档库采用模块化组织方式，按照系统的三大核心功能模块和文档用途进行分类，便于用户快速找到所需信息。

```
doc/
├── README.md                    # 文档导航（本文件）
├── 01-项目文档/                 # 项目整体文档
│   ├── PRD_A股选股系统.md       # 产品需求文档
│   ├── 架构设计文档.md          # 技术架构设计
│   └── 开发计划.md              # 版本开发计划
├── 02-数据更新模块/             # 数据获取和更新
│   ├── 数据更新模块使用指南.md   # 模块使用指南（合并）
│   ├── 数据获取功能说明.md       # 功能详细说明
│   ├── Tushare数据源配置指南.md # 数据源配置
│   └── Token配置文件管理说明.md # Token管理
├── 03-策略选股模块/             # 策略选股功能
│   ├── 策略选股模块使用指南.md   # 模块使用指南（合并）
│   ├── 策略选股模块说明.md       # 功能详细说明
│   ├── 技术反转策略设计文档.md   # 策略设计文档
│   └── 候选股票池控制功能使用指南.md # 股票池控制
├── 04-可视化验证模块/           # 可视化验证功能
│   ├── 可视化验证模块使用指南.md # 模块使用指南（合并）
│   ├── 可视化验证模块说明.md     # 功能详细说明
│   ├── 策略可视化验证完整指南.md # 完整验证指南
│   └── 策略验证功能使用指南.md   # 验证功能说明
└── 05-开发文档/                 # 开发相关文档
    └── 项目整理总结.md          # 项目整理总结
```

## 🎯 快速导航

### 新用户入门
如果您是第一次使用本系统，建议按以下顺序阅读：

1. **了解系统** → [产品需求文档](01-项目文档/PRD_A股选股系统.md)
2. **系统架构** → [架构设计文档](01-项目文档/架构设计文档.md)
3. **数据准备** → [数据更新模块使用指南](02-数据更新模块/数据更新模块使用指南.md)
4. **策略选股** → [策略选股模块使用指南](03-策略选股模块/策略选股模块使用指南.md)
5. **结果验证** → [可视化验证模块使用指南](04-可视化验证模块/可视化验证模块使用指南.md)

### 按功能模块查找

#### 🔄 数据更新模块
- **快速上手**: [数据更新模块使用指南](02-数据更新模块/数据更新模块使用指南.md)
- **功能详解**: [数据获取功能说明](02-数据更新模块/数据获取功能说明.md)
- **配置指南**: [Tushare数据源配置指南](02-数据更新模块/Tushare数据源配置指南.md)
- **Token管理**: [Token配置文件管理说明](02-数据更新模块/Token配置文件管理说明.md)

#### 📊 策略选股模块
- **快速上手**: [策略选股模块使用指南](03-策略选股模块/策略选股模块使用指南.md)
- **功能详解**: [策略选股模块说明](03-策略选股模块/策略选股模块说明.md)
- **策略设计**: [技术反转策略设计文档](03-策略选股模块/技术反转策略设计文档.md)
- **股票池控制**: [候选股票池控制功能使用指南](03-策略选股模块/候选股票池控制功能使用指南.md)

#### 📈 可视化验证模块
- **快速上手**: [可视化验证模块使用指南](04-可视化验证模块/可视化验证模块使用指南.md)
- **功能详解**: [可视化验证模块说明](04-可视化验证模块/可视化验证模块说明.md)
- **完整指南**: [策略可视化验证完整指南](04-可视化验证模块/策略可视化验证完整指南.md)
- **验证功能**: [策略验证功能使用指南](04-可视化验证模块/策略验证功能使用指南.md)

### 按文档类型查找

#### 📋 使用指南类（推荐新用户）
- [数据更新模块使用指南](02-数据更新模块/数据更新模块使用指南.md) - 数据获取和更新操作
- [策略选股模块使用指南](03-策略选股模块/策略选股模块使用指南.md) - 策略选股操作
- [可视化验证模块使用指南](04-可视化验证模块/可视化验证模块使用指南.md) - 可视化验证操作

#### 🔧 设计文档类（开发人员）
- [架构设计文档](01-项目文档/架构设计文档.md) - 系统技术架构
- [技术反转策略设计文档](03-策略选股模块/技术反转策略设计文档.md) - 策略算法设计

#### 📈 开发计划类（项目管理）
- [开发计划](01-项目文档/开发计划.md) - 版本开发规划
- [项目整理总结](05-开发文档/项目整理总结.md) - 项目整理记录

## 🚀 快速开始

### 系统初始化
```bash
# 1. 数据准备
python src/main.py --update-stocks --historical

# 2. 策略选股
python src/run_selection.py

# 3. 结果验证
python scripts/quick_validate.py 601111 2024-04-01 2024-04-30
```

### 常用操作
```bash
# 每日数据更新
python src/main.py --incremental

# 运行选股策略
python src/run_selection.py --strategy technical_reversal

# 可视化验证
python scripts/interactive_visual_validation.py
```

## 📖 文档特色

### 🎯 模块化组织
- 按照系统三大核心模块分类
- 每个模块有独立的使用指南
- 便于按需查找和学习

### 📚 分层文档
- **使用指南**: 面向最终用户，操作导向
- **功能说明**: 详细的功能介绍和技术细节
- **设计文档**: 面向开发人员，架构和算法设计

### 🔄 合并优化
- 将多个相似的使用指南合并为模块使用指南
- 减少文档冗余，提高查找效率
- 保持内容的完整性和一致性

## 💡 使用建议

### 新用户学习路径
1. **理解系统** → 阅读PRD和架构文档
2. **动手实践** → 按模块使用指南操作
3. **深入学习** → 阅读功能说明和设计文档
4. **高级应用** → 参考开发文档进行定制

### 问题解决路径
1. **操作问题** → 查看对应模块的使用指南
2. **功能疑问** → 查看功能说明文档
3. **技术问题** → 查看设计文档和开发文档
4. **配置问题** → 查看配置指南

### 文档维护
- 使用指南保持最新的操作步骤
- 功能说明反映最新的功能特性
- 设计文档记录架构演进
- 及时更新版本信息

## 📞 技术支持

如果在使用过程中遇到问题：

1. **查看对应模块的使用指南**
2. **运行系统测试脚本**
3. **检查日志文件**
4. **参考故障排除部分**

---

**文档版本**: v2.0  
**最后更新**: 2024年12月  
**维护说明**: 采用模块化组织，定期更新内容
