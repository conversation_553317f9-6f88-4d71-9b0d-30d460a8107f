# 可视化展示模块使用指南

## 🎯 模块概述

可视化展示模块是A股智能选股系统的分析展示模块，通过K线图表和技术指标可视化展示策略信号，帮助用户分析策略效果和买入时机。该模块支持单股票展示、批量展示和交互式操作。

## 🚀 快速开始

### 前置条件
```bash
# 1. 激活虚拟环境
source venv/bin/activate

# 2. 确认系统正常
python scripts/test_enhanced_features.py

# 3. 安装可视化依赖
pip install matplotlib mplfinance
```

### 基本使用
```bash
# 快速展示单只股票
python scripts/quick_validate.py 601111 2024-04-01 2024-04-30

# 启动交互式界面
python scripts/interactive_visual_validation.py

# 运行完整演示
python scripts/complete_visual_demo.py
```

## 📋 使用方式

### 1. 一键展示（最简单）

**适用场景**: 快速展示单只股票的策略效果

**使用方法**:
```bash
# 基本语法
python scripts/quick_validate.py <股票代码> <开始日期> <结束日期>

# 实际示例
python scripts/quick_validate.py 601111 2024-04-01 2024-04-30
python scripts/quick_validate.py 600036 2024-03-01 2024-03-31
python scripts/quick_validate.py 000001 2024-05-01 2024-05-31
```

**输出结果**:
```
🎯 展示 601111 (2024-04-01 至 2024-04-30)
✅ 成功: 发现 3 个信号
📊 图表: charts/601111_technical_reversal_20250531_163642.png
  1. 2024-04-12 价格:7.09 评分:103.0
  2. 2024-04-15 价格:7.20 评分:84.1
  3. 2024-04-19 价格:7.09 评分:84.3
```

### 2. 交互式界面（最用户友好）

**适用场景**: 不熟悉命令行或需要多种操作

**启动方法**:
```bash
python scripts/interactive_visual_validation.py
```

**操作步骤**:
1. 选择 `1` (单股票展示)
2. 输入股票代码: `601111`
3. 选择策略: `1` (technical_reversal)
4. 输入开始日期: `2024-04-01`
5. 输入结束日期: `2024-04-30`
6. 选择是否查看详细报告: `y` 或 `n`

**功能菜单**:
```
=== 策略可视化展示系统 ===
1. 单股票展示
2. 批量股票展示
3. 策略比较分析
4. 历史信号回顾
5. 系统状态检查
0. 退出系统
```

### 3. 自定义展示脚本

**适用场景**: 需要更多控制和详细输出

**使用方法**:
```bash
# 命令行参数方式
python scripts/custom_validation.py <股票代码> <开始日期> <结束日期> [策略名称]

# 实际示例
python scripts/custom_validation.py 601111 2024-04-01 2024-04-30
python scripts/custom_validation.py 600036 2024-03-01 2024-03-31 technical_reversal

# 或者修改脚本中的预设参数后直接运行
python scripts/custom_validation.py
```

### 4. 预设演示脚本

**适用场景**: 快速体验功能或学习使用

**使用方法**:
```bash
# 运行完整演示（中国国航 2024年3-5月）
python scripts/demo_visual_validation.py

# 运行增强功能演示
python scripts/complete_visual_demo.py

# 快速演示模式
python scripts/complete_visual_demo.py quick
```

## 📊 参数说明

### 股票代码
- **格式**: 6位数字
- **示例**: `601111` (中国国航), `600036` (招商银行), `000001` (平安银行)
- **注意**: 必须是数据库中已有数据的股票

### 日期格式
- **格式**: `YYYY-MM-DD`
- **示例**: `2024-04-01`, `2024-03-15`, `2024-05-31`
- **注意**: 开始日期必须早于结束日期

### 策略名称
- **默认**: `technical_reversal` (技术反转策略)
- **可选**: 系统中已配置的其他策略
- **注意**: 大部分情况使用默认策略即可

## 🎨 图表解读

### 主要元素
1. **K线图**: 红色上涨，绿色下跌，显示价格走势
2. **布林带**: 蓝色区域，显示价格通道和支撑阻力
3. **信号点**: 红色三角形，标注策略买入信号位置
4. **成交量**: 下方柱状图，显示成交量变化
5. **RSI指标**: 底部指标线，显示超买超卖状态

### 信号标注
- **红色三角形**: 买入信号位置
- **黄色标签**: 信号编号和说明
- **白色框**: 评分和RSI值详情
- **红色虚线**: 信号日期标记线

### 技术指标
- **布林带上轨**: 阻力位参考
- **布林带中轨**: 移动平均线
- **布林带下轨**: 支撑位参考，策略关注点
- **RSI < 30**: 超卖区域，反弹机会

## 📁 输出文件

### 图表文件
- **位置**: `charts/` 目录
- **格式**: PNG图片文件
- **命名**: `{股票代码}_{策略名称}_{时间戳}.png`
- **内容**: 完整的K线图表和技术指标

### 分析结果
- **位置**: `results/` 目录（批量展示时）
- **格式**: JSON文件
- **内容**: 完整的分析数据和信号详情

### 日志文件
- **位置**: `logs/` 目录
- **格式**: 文本日志
- **内容**: 展示过程和错误信息

## 💡 使用建议

### 选择合适的时间周期
- **短期分析**: 1-2周 (如: 2024-04-01 到 2024-04-15)
- **中期分析**: 1-3个月 (如: 2024-04-01 到 2024-06-30)
- **长期分析**: 3-6个月 (如: 2024-01-01 到 2024-06-30)

### 股票选择建议
- **活跃股票**: 成交量较大的主板股票
- **有数据股票**: 确保数据库中有相关交易数据
- **避免停牌**: 选择正常交易的股票

### 结果分析要点
1. **信号数量**: 过多可能策略过于宽松，过少可能过于严格
2. **信号质量**: 关注评分和RSI值
3. **时间分布**: 观察信号在时间上的分布规律
4. **价格位置**: 信号出现的价格区间

## 🔍 高级功能

### 批量展示
```bash
# 使用交互式界面的批量展示功能
python scripts/interactive_visual_validation.py
# 选择选项 2 (批量股票展示)

# 或使用增强功能演示
python scripts/complete_visual_demo.py
```

### 策略比较
```bash
# 比较不同策略在同一股票上的表现
python scripts/strategy_comparison.py --stock 601111 --start-date 2024-04-01 --end-date 2024-04-30
```

### 自定义展示脚本
用户可以修改 `scripts/custom_validation.py` 中的参数：

```python
# 修改这些参数
STOCK_CODE = "601111"        # 股票代码
START_DATE = "2024-04-01"    # 开始日期
END_DATE = "2024-04-30"      # 结束日期
STRATEGY_NAME = "technical_reversal"  # 策略名称
```

### 批量展示多个时间段
```python
# 可以编写循环脚本展示多个时间段
time_periods = [
    ("2024-03-01", "2024-03-31"),
    ("2024-04-01", "2024-04-30"),
    ("2024-05-01", "2024-05-31")
]

for start, end in time_periods:
    # 执行展示
    os.system(f"python scripts/quick_validate.py 601111 {start} {end}")
```

## ❓ 常见问题

### Q1: 展示失败怎么办？
```bash
# 检查数据库连接
python scripts/test_enhanced_features.py

# 确认股票代码正确
# 检查日期范围内是否有交易数据
```

### Q2: 没有发现信号是否正常？
是的，这表明在指定时间周期内，该股票不符合策略条件。可以：
- 尝试更长的时间周期
- 选择其他股票
- 检查策略参数设置

### Q3: 图表显示异常怎么办？
```bash
# 检查matplotlib配置
python -c "import matplotlib.pyplot as plt; print('OK')"

# 重新生成图表
rm charts/*.png
python scripts/quick_validate.py 601111 2024-04-01 2024-04-30
```

### Q4: 如何解读信号质量？
- **评分>90**: 强烈信号，技术指标明确
- **评分70-90**: 中等信号，可以关注
- **评分50-70**: 弱信号，需要谨慎

## 📞 技术支持

如遇问题，请：
1. 查看日志文件 `logs/app.log`
2. 运行测试脚本检查系统状态
3. 确认数据库连接和数据完整性
4. 检查图表文件是否正常生成

---

**推荐使用顺序**:
1. 新手用户: 交互式界面 → 预设演示 → 一键展示
2. 熟练用户: 一键展示 → 自定义脚本 → 批量处理
3. 开发用户: 直接调用API → 自定义功能扩展
