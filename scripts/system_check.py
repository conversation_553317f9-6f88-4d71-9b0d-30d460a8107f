#!/usr/bin/env python3
"""
系统状态检查工具
检查A股选股系统的各个模块是否正常工作
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from src.data.access.mysql_access import MySQLDataAccess
    from src.strategies.strategy_manager import StrategyManager
    from src.config.tushare_config import TushareConfig
except ImportError as e:
    print(f"❌ 导入模块失败: {str(e)}")
    print("💡 请确保在项目根目录运行此脚本")
    sys.exit(1)


def check_database_connection():
    """检查数据库连接"""
    print("🔍 检查数据库连接...")
    try:
        data_access = MySQLDataAccess()
        stock_codes = data_access.get_all_stock_codes()
        print(f"✅ 数据库连接正常，共有 {len(stock_codes)} 只股票数据")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False


def check_tushare_config():
    """检查Tushare配置"""
    print("\n🔍 检查Tushare配置...")
    try:
        config = TushareConfig()
        if config.is_token_configured():
            print("✅ Tushare Token已配置")
            return True
        else:
            print("❌ Tushare Token未配置")
            print("💡 请运行: python scripts/setup_tushare_token.py")
            return False
    except Exception as e:
        print(f"❌ Tushare配置检查失败: {str(e)}")
        return False


def check_strategies():
    """检查策略模块"""
    print("\n🔍 检查策略模块...")
    try:
        data_access = MySQLDataAccess()
        strategy_manager = StrategyManager(data_access)
        strategies = strategy_manager.get_available_strategies()
        print(f"✅ 策略模块正常，可用策略: {', '.join(strategies)}")
        return True
    except Exception as e:
        print(f"❌ 策略模块检查失败: {str(e)}")
        return False


def check_directories():
    """检查目录结构"""
    print("\n🔍 检查目录结构...")
    required_dirs = ['src', 'scripts', 'config', 'charts', 'results', 'logs', 'doc']
    missing_dirs = []
    
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            missing_dirs.append(dir_name)
            os.makedirs(dir_name, exist_ok=True)
    
    if missing_dirs:
        print(f"⚠️ 创建缺失目录: {', '.join(missing_dirs)}")
    
    print("✅ 目录结构检查完成")
    return True


def check_core_files():
    """检查核心文件"""
    print("\n🔍 检查核心文件...")
    core_files = [
        'src/main.py',
        'run_selection.py',
        'scripts/setup_tushare_token.py',
        'scripts/interactive_visual_validation.py',
        'scripts/demo_visual_validation.py',

    ]
    
    missing_files = []
    for file_path in core_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺失核心文件: {', '.join(missing_files)}")
        return False
    else:
        print("✅ 核心文件检查完成")
        return True


def main():
    """主函数"""
    print("🚀 A股选股系统状态检查")
    print("=" * 50)
    
    checks = [
        ("目录结构", check_directories),
        ("核心文件", check_core_files),
        ("数据库连接", check_database_connection),
        ("Tushare配置", check_tushare_config),
        ("策略模块", check_strategies),
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}检查时发生异常: {str(e)}")
            results.append((check_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 检查结果总结")
    print("=" * 50)
    
    all_passed = True
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{check_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 系统状态良好，所有检查都通过！")
        print("\n💡 接下来可以:")
        print("1. 运行数据更新: python src/main.py --incremental")
        print("2. 执行选股策略: python run_selection.py select --strategy technical_reversal")
        print("3. 可视化验证: python scripts/interactive_visual_validation.py")
    else:
        print("⚠️ 系统存在问题，请根据上述检查结果进行修复")
    
    return all_passed


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 用户中断检查")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 检查过程中发生异常: {str(e)}")
        sys.exit(1)
