#!/usr/bin/env python3
"""
交互式可视化策略展示界面
"""
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.access.mysql_access import MySQLDataAccess
from src.validation.enhanced_visual_validator import EnhancedVisualValidator
from src.core.interfaces.visual_validator import VisualValidationConfig
from src.utils.logger import setup_logger


class InteractiveValidator:
    """交互式验证器"""

    def __init__(self):
        self.data_access = MySQLDataAccess()
        self.validator = EnhancedVisualValidator(self.data_access)
        self.logger = setup_logger('interactive_validator')

        # 预定义的股票池
        self.stock_pools = {
            '1': ['601111', '600036', '000001'],  # 测试股票池
            '2': ['600519', '000858', '002415'],  # 白酒医药
            '3': ['600036', '601318', '000001'],  # 银行股
        }

        # 预定义的策略
        self.strategies = {
            '1': 'technical_reversal',
            '2': 'volume_anomaly'
        }

    def show_menu(self):
        """显示主菜单"""
        print("\n" + "="*60)
        print("🎯 可视化策略验证系统")
        print("="*60)
        print("1. 单股票验证")
        print("2. 批量股票验证")
        print("3. 策略效果比较")
        print("4. 查看历史验证结果")
        print("5. 系统设置")
        print("0. 退出系统")
        print("="*60)

    def get_user_input(self, prompt: str, default: str = None) -> str:
        """获取用户输入"""
        if default:
            user_input = input(f"{prompt} (默认: {default}): ").strip()
            return user_input if user_input else default
        else:
            return input(f"{prompt}: ").strip()

    def get_date_input(self, prompt: str, default_days_ago: int = 30) -> datetime:
        """获取日期输入"""
        default_date = datetime.now() - timedelta(days=default_days_ago)

        while True:
            date_str = self.get_user_input(
                f"{prompt} (格式: YYYY-MM-DD)",
                default_date.strftime('%Y-%m-%d')
            )

            try:
                parsed_date = datetime.strptime(date_str, '%Y-%m-%d')
                print(f"✅ 已选择日期: {parsed_date.date()}")
                return parsed_date
            except ValueError:
                print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式，如: 2024-04-01")
                use_default = self.get_user_input("是否使用默认日期? (y/n)", "y")
                if use_default.lower() == 'y':
                    print(f"✅ 使用默认日期: {default_date.date()}")
                    return default_date
                print("请重新输入日期:")

    def single_stock_validation(self):
        """单股票验证"""
        print("\n📊 单股票可视化验证")
        print("-" * 40)

        # 获取股票代码
        stock_code = self.get_user_input("请输入股票代码", "601111")

        # 获取策略
        print("\n可用策略:")
        for key, strategy in self.strategies.items():
            print(f"  {key}. {strategy}")

        strategy_choice = self.get_user_input("选择策略", "1")
        strategy_name = self.strategies.get(strategy_choice, 'technical_reversal')

        # 获取日期范围
        print("\n📅 设置验证时间范围:")
        start_date = self.get_date_input("开始日期", 90)
        end_date = self.get_date_input("结束日期", 0)

        # 验证日期范围
        if start_date >= end_date:
            print("❌ 开始日期必须早于结束日期")
            print("💡 自动调整为合理的日期范围")
            end_date = start_date + timedelta(days=30)
            print(f"✅ 调整后的结束日期: {end_date.date()}")

        # 检查日期范围是否过长
        date_diff = (end_date - start_date).days
        if date_diff > 180:
            print(f"⚠️ 验证时间范围较长 ({date_diff} 天)，可能影响性能")
            continue_choice = self.get_user_input("是否继续? (y/n)", "y")
            if continue_choice.lower() != 'y':
                print("❌ 用户取消验证")
                return

        print(f"\n🔍 开始验证 {stock_code} 使用策略 {strategy_name}")
        print(f"📅 验证期间: {start_date.date()} 至 {end_date.date()} ({date_diff} 天)")

        try:
            # 执行验证
            config = VisualValidationConfig(
                stock_code=stock_code,
                strategy_name=strategy_name,
                start_date=start_date,
                end_date=end_date,
                show_indicators=True,
                show_volume=True
            )

            result = self.validator.validate_stock_visual(config)

            if result.error_message:
                print(f"❌ 验证失败: {result.error_message}")
                return

            # 显示结果
            print(f"\n✅ 验证完成!")
            print(f"📈 股票名称: {result.stock_name}")
            print(f"🎯 发现信号: {result.total_signals} 个")

            if result.chart_path:
                print(f"📊 图表文件: {result.chart_path}")

            # 显示信号详情
            if result.signal_points:
                print(f"\n📋 信号详情:")
                for i, signal in enumerate(result.signal_points, 1):
                    print(f"  {i}. {signal.date.date()} - 价格:{signal.price:.2f} - 评分:{signal.score:.1f}")

            # 询问是否查看详细报告
            if self.get_user_input("是否查看详细报告? (y/n)", "n").lower() == 'y':
                print("\n" + "="*80)
                print(result.validation_summary)
                print("="*80)

        except Exception as e:
            print(f"❌ 验证过程出错: {str(e)}")

    def batch_validation(self):
        """批量验证"""
        print("\n📊 批量股票验证")
        print("-" * 40)

        # 选择股票池
        print("\n可用股票池:")
        for key, stocks in self.stock_pools.items():
            print(f"  {key}. {stocks}")
        print("  4. 自定义股票池")

        pool_choice = self.get_user_input("选择股票池", "1")

        if pool_choice == '4':
            # 自定义股票池
            stock_codes_str = self.get_user_input("请输入股票代码(用逗号分隔)", "601111,600036")
            stock_codes = [code.strip() for code in stock_codes_str.split(',')]
        else:
            stock_codes = self.stock_pools.get(pool_choice, self.stock_pools['1'])

        # 选择策略
        print("\n可用策略:")
        for key, strategy in self.strategies.items():
            print(f"  {key}. {strategy}")

        strategy_choice = self.get_user_input("选择策略", "1")
        strategy_name = self.strategies.get(strategy_choice, 'technical_reversal')

        # 获取日期范围
        start_date = self.get_date_input("开始日期", 60)
        end_date = self.get_date_input("结束日期", 0)

        print(f"\n🔍 开始批量验证 {len(stock_codes)} 只股票")
        print(f"📅 验证期间: {start_date.date()} 至 {end_date.date()}")
        print(f"🎯 使用策略: {strategy_name}")

        try:
            # 执行批量验证
            results = self.validator.batch_validate_stocks(
                stock_codes, strategy_name, start_date, end_date
            )

            # 生成报告
            report = self.validator.generate_batch_report(results)
            print("\n" + report)

            # 保存结果
            if self.get_user_input("是否保存验证结果? (y/n)", "y").lower() == 'y':
                filepath = self.validator.save_validation_results(results)
                print(f"📁 结果已保存到: {filepath}")

        except Exception as e:
            print(f"❌ 批量验证出错: {str(e)}")

    def strategy_comparison(self):
        """策略比较"""
        print("\n📊 策略效果比较")
        print("-" * 40)

        # 选择股票池
        print("\n选择测试股票池:")
        for key, stocks in self.stock_pools.items():
            print(f"  {key}. {stocks}")

        pool_choice = self.get_user_input("选择股票池", "1")
        stock_codes = self.stock_pools.get(pool_choice, self.stock_pools['1'])

        # 选择要比较的策略
        print("\n选择要比较的策略 (多选用逗号分隔):")
        for key, strategy in self.strategies.items():
            print(f"  {key}. {strategy}")

        strategy_choices = self.get_user_input("选择策略", "1,2").split(',')
        strategy_names = [self.strategies.get(choice.strip(), 'technical_reversal')
                         for choice in strategy_choices]

        # 获取日期范围
        start_date = self.get_date_input("开始日期", 60)
        end_date = self.get_date_input("结束日期", 0)

        print(f"\n🔍 开始策略比较")
        print(f"📅 测试期间: {start_date.date()} 至 {end_date.date()}")
        print(f"🎯 比较策略: {', '.join(strategy_names)}")
        print(f"📊 测试股票: {', '.join(stock_codes)}")

        try:
            # 执行策略比较
            strategy_results = self.validator.compare_strategies(
                stock_codes, strategy_names, start_date, end_date
            )

            # 生成比较报告
            report = self.validator.generate_strategy_comparison_report(strategy_results)
            print("\n" + report)

        except Exception as e:
            print(f"❌ 策略比较出错: {str(e)}")

    def view_history(self):
        """查看历史结果"""
        print("\n📋 历史验证结果")
        print("-" * 40)

        # 检查结果目录
        results_dir = 'results'
        if not os.path.exists(results_dir):
            print("📁 暂无历史验证结果")
            return

        # 列出结果文件
        result_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]

        if not result_files:
            print("📁 暂无历史验证结果")
            return

        print("📁 可用的历史结果:")
        for i, filename in enumerate(result_files, 1):
            print(f"  {i}. {filename}")

        # 这里可以添加查看具体结果文件的功能
        print("💡 提示: 可以直接查看 results/ 目录下的JSON文件")

    def system_settings(self):
        """系统设置"""
        print("\n⚙️ 系统设置")
        print("-" * 40)
        print("1. 查看数据库连接状态")
        print("2. 清理临时文件")
        print("3. 查看系统信息")

        choice = self.get_user_input("选择操作", "1")

        if choice == '1':
            try:
                # 测试数据库连接
                stock_codes = self.data_access.get_all_stock_codes()
                print(f"✅ 数据库连接正常，共有 {len(stock_codes)} 只股票数据")
            except Exception as e:
                print(f"❌ 数据库连接失败: {str(e)}")

        elif choice == '2':
            # 清理临时文件
            import glob
            temp_files = glob.glob('charts/*.png') + glob.glob('results/*.json')
            if temp_files:
                print(f"🗑️ 找到 {len(temp_files)} 个临时文件")
                if self.get_user_input("是否删除? (y/n)", "n").lower() == 'y':
                    for file in temp_files:
                        os.remove(file)
                    print("✅ 临时文件已清理")
            else:
                print("📁 没有找到临时文件")

        elif choice == '3':
            print("📊 系统信息:")
            print(f"  Python版本: {sys.version}")
            print(f"  工作目录: {os.getcwd()}")
            print(f"  图表目录: charts/")
            print(f"  结果目录: results/")

    def run(self):
        """运行交互式界面"""
        print("🚀 欢迎使用可视化策略验证系统!")

        while True:
            try:
                self.show_menu()
                choice = self.get_user_input("请选择操作")

                if choice == '0':
                    print("👋 感谢使用，再见!")
                    break
                elif choice == '1':
                    self.single_stock_validation()
                elif choice == '2':
                    self.batch_validation()
                elif choice == '3':
                    self.strategy_comparison()
                elif choice == '4':
                    self.view_history()
                elif choice == '5':
                    self.system_settings()
                else:
                    print("❌ 无效选择，请重新输入")

                # 询问是否继续
                if choice != '0':
                    input("\n按回车键继续...")

            except KeyboardInterrupt:
                print("\n\n👋 用户中断，退出系统")
                break
            except Exception as e:
                print(f"\n❌ 系统错误: {str(e)}")
                input("按回车键继续...")


if __name__ == '__main__':
    validator = InteractiveValidator()
    validator.run()
