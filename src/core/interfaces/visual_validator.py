"""
可视化策略展示器接口定义
"""
from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass


@dataclass
class VisualValidationConfig:
    """可视化展示配置"""
    stock_code: str  # 股票代码
    strategy_name: str  # 策略名称
    start_date: datetime  # 开始日期
    end_date: datetime  # 结束日期
    chart_title: Optional[str] = None  # 图表标题
    save_path: Optional[str] = None  # 保存路径
    show_indicators: bool = True  # 是否显示技术指标
    show_volume: bool = True  # 是否显示成交量
    figure_size: Tuple[int, int] = (15, 10)  # 图表大小


@dataclass
class SignalPoint:
    """信号点数据"""
    date: datetime  # 信号日期
    price: float  # 信号价格
    signal_type: str  # 信号类型（buy, sell等）
    score: float  # 策略评分
    indicators: Dict  # 相关指标值
    reason: str  # 信号原因


@dataclass
class VisualValidationResult:
    """可视化展示结果"""
    config: VisualValidationConfig
    stock_name: str  # 股票名称
    signal_points: List[SignalPoint]  # 信号点列表
    chart_path: Optional[str] = None  # 图表保存路径
    total_signals: int = 0  # 总信号数量
    validation_summary: str = ""  # 展示总结
    error_message: Optional[str] = None  # 错误信息


class IVisualValidator(ABC):
    """可视化策略展示器接口"""

    @abstractmethod
    def validate_stock_visual(self, config: VisualValidationConfig) -> VisualValidationResult:
        """
        对单只股票进行可视化策略展示

        Args:
            config: 可视化展示配置

        Returns:
            VisualValidationResult: 可视化展示结果
        """
        pass

    @abstractmethod
    def generate_chart(self, 
                      stock_code: str,
                      stock_data: List[Dict],
                      signal_points: List[SignalPoint],
                      config: VisualValidationConfig) -> str:
        """
        生成K线图表
        
        Args:
            stock_code: 股票代码
            stock_data: 股票交易数据
            signal_points: 信号点列表
            config: 可视化配置
            
        Returns:
            str: 图表保存路径
        """
        pass

    @abstractmethod
    def find_strategy_signals(self,
                            stock_code: str,
                            strategy_name: str,
                            start_date: datetime,
                            end_date: datetime) -> List[SignalPoint]:
        """
        查找策略信号点
        
        Args:
            stock_code: 股票代码
            strategy_name: 策略名称
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[SignalPoint]: 信号点列表
        """
        pass
